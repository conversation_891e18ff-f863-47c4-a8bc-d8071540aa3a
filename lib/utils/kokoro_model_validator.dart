import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Utility class for validating KOKORO-82M model files
class KokoroModelValidator {
  static const String _modelPath = 'assets/models/kokoro/';
  
  /// Expected file sizes for validation (in bytes)
  static const Map<String, int> _expectedFileSizes = {
    'model_q8f16.onnx': 86 * 1024 * 1024, // ~86MB
    'zf_xiaobei.bin': 522 * 1024, // ~522KB
    'zf_xiaoni.bin': 522 * 1024, // ~522KB
    'zf_xiaoxiao.bin': 522 * 1024, // ~522KB
    'tokenizer.json': 3500, // ~3.5KB
    'config.json': 100, // ~100 bytes
  };
  
  /// Required files for KOKORO TTS to function
  static const List<String> _requiredFiles = [
    'model_q8f16.onnx',
    'zf_xiaobei.bin', // Default Chinese voice
    'tokenizer.json',
    'config.json',
  ];
  
  /// Optional Chinese voice files
  static const List<String> _optionalVoiceFiles = [
    'zf_xiaoni.bin',
    'zf_xiaoxiao.bin',
  ];
  
  /// Validates all KOKORO model files
  /// Returns true if all required files are present and valid
  static Future<bool> validateAllFiles() async {
    try {
      AnxLog.info('Starting KOKORO model validation...');
      
      // Check required files
      for (String fileName in _requiredFiles) {
        if (!await _validateFile(fileName)) {
          AnxLog.severe('Required KOKORO file missing or invalid: $fileName');
          return false;
        }
      }
      
      // Check optional voice files (log warnings but don't fail)
      for (String fileName in _optionalVoiceFiles) {
        if (!await _validateFile(fileName)) {
          AnxLog.warning('Optional KOKORO voice file missing: $fileName');
        }
      }
      
      AnxLog.info('KOKORO model validation completed successfully');
      return true;
    } catch (e) {
      AnxLog.severe('KOKORO model validation failed: $e');
      return false;
    }
  }
  
  /// Validates a specific model file
  static Future<bool> _validateFile(String fileName) async {
    try {
      final String assetPath = '$_modelPath$fileName';
      
      // Check if file exists in assets
      final ByteData data = await rootBundle.load(assetPath);
      final int actualSize = data.lengthInBytes;
      
      // Check file size (allow 10% variance for compression differences)
      if (_expectedFileSizes.containsKey(fileName)) {
        final int expectedSize = _expectedFileSizes[fileName]!;
        final double variance = (actualSize - expectedSize).abs() / expectedSize;
        
        if (variance > 0.1) { // 10% tolerance
          AnxLog.warning(
            'KOKORO file size mismatch for $fileName: '
            'expected ~${_formatBytes(expectedSize)}, '
            'got ${_formatBytes(actualSize)}'
          );
        }
      }
      
      // Basic integrity check - ensure file is not empty
      if (actualSize == 0) {
        AnxLog.severe('KOKORO file is empty: $fileName');
        return false;
      }
      
      AnxLog.info('KOKORO file validated: $fileName (${_formatBytes(actualSize)})');
      return true;
    } catch (e) {
      AnxLog.severe('Failed to validate KOKORO file $fileName: $e');
      return false;
    }
  }
  
  /// Checks if KOKORO model is available and ready to use
  static Future<bool> isModelReady() async {
    return await validateAllFiles();
  }
  
  /// Gets the list of available Chinese voices
  static Future<List<String>> getAvailableChineseVoices() async {
    List<String> availableVoices = [];
    
    // Check all Chinese voice files
    List<String> allVoiceFiles = [
      'zf_xiaobei.bin',
      'zf_xiaoni.bin', 
      'zf_xiaoxiao.bin',
    ];
    
    for (String voiceFile in allVoiceFiles) {
      if (await _validateFile(voiceFile)) {
        // Extract voice name from filename (remove .bin extension)
        String voiceName = voiceFile.replaceAll('.bin', '');
        availableVoices.add(voiceName);
      }
    }
    
    return availableVoices;
  }
  
  /// Formats bytes into human-readable format
  static String _formatBytes(int bytes) {
    if (bytes < 1024) return '${bytes}B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
  
  /// Gets model information for debugging
  static Future<Map<String, dynamic>> getModelInfo() async {
    Map<String, dynamic> info = {
      'isReady': await isModelReady(),
      'availableVoices': await getAvailableChineseVoices(),
      'fileStatus': <String, dynamic>{},
    };
    
    // Get status of all files
    List<String> allFiles = [..._requiredFiles, ..._optionalVoiceFiles];
    for (String fileName in allFiles) {
      try {
        final String assetPath = '$_modelPath$fileName';
        final ByteData data = await rootBundle.load(assetPath);
        info['fileStatus'][fileName] = {
          'exists': true,
          'size': data.lengthInBytes,
          'sizeFormatted': _formatBytes(data.lengthInBytes),
        };
      } catch (e) {
        info['fileStatus'][fileName] = {
          'exists': false,
          'error': e.toString(),
        };
      }
    }
    
    return info;
  }
}

# KOKORO-82M Model Files

This directory contains the KOKORO-82M TTS model files for enhanced Chinese pronunciation in DassoShu Reader.

## Required Files

### Model File (Choose one based on device capabilities)
- `model_q8f16.onnx` (86MB) - **RECOMMENDED** - Best balance of quality and size for mobile
- `model_quantized.onnx` (92.4MB) - Alternative quantized version
- `model_fp16.onnx` (163MB) - Higher quality, larger size

### Chinese Voice Files (Required for Chinese TTS)
- `zf_xiaobei.bin` (522KB) - Chinese female voice
- `zf_xiaoni.bin` (522KB) - Chinese female voice  
- `zf_xiaoxiao.bin` (522KB) - Chinese female voice

### Configuration Files
- `tokenizer.json` (3.5KB) - Text tokenization configuration
- `config.json` (44 bytes) - Model configuration

## Download Instructions

To download the model files, run these commands from the project root:

```bash
# Create directory
mkdir -p assets/models/kokoro

# Download optimized model (86MB)
curl -L -o assets/models/kokoro/model_q8f16.onnx \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/onnx/model_q8f16.onnx"

# Download Chinese voice files
curl -L -o assets/models/kokoro/zf_xiaobei.bin \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/voices/zf_xiaobei.bin"
curl -L -o assets/models/kokoro/zf_xiaoni.bin \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/voices/zf_xiaoni.bin"
curl -L -o assets/models/kokoro/zf_xiaoxiao.bin \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/voices/zf_xiaoxiao.bin"

# Download configuration files
curl -L -o assets/models/kokoro/tokenizer.json \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/tokenizer.json"
curl -L -o assets/models/kokoro/config.json \
  "https://huggingface.co/onnx-community/Kokoro-82M-v1.0-ONNX/resolve/main/config.json"
```

## Model Validation

After downloading, verify the files:
- Total size should be approximately 87-88MB
- All files should be present and non-zero size
- Model file should be exactly 86MB (model_q8f16.onnx)

## Usage

The KokoroTts class will automatically load these files when Chinese text is detected for TTS synthesis.

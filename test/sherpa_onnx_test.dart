import 'package:flutter_test/flutter_test.dart';
import 'package:sherpa_onnx/sherpa_onnx.dart';

void main() {
  group('Sherpa ONNX Basic Tests', () {
    test('sherpa_onnx package can be imported', () {
      // This test verifies that the sherpa_onnx package is properly installed
      // and can be imported without issues
      expect(true, isTrue);
    });

    test('OfflineTtsConfig can be created', () {
      // Test basic configuration creation for TTS
      try {
        final config = OfflineTtsConfig(
          model: OfflineTtsModelConfig(
            vits: OfflineTtsVitsModelConfig(
              model: 'test-model.onnx',
              lexicon: 'test-lexicon.txt',
              tokens: 'test-tokens.txt',
            ),
          ),
          ruleFsts: 'test-rule.fst',
          maxNumSentences: 1,
        );
        
        expect(config, isNotNull);
        expect(config.maxNumSentences, equals(1));
      } catch (e) {
        // Expected to fail since we don't have actual model files
        // This test just verifies the API is available
        expect(e, isA<Exception>());
      }
    });

    test('OfflineTts class is available', () {
      // Test that the OfflineTts class can be referenced
      // This verifies the API structure is correct
      expect(OfflineTts, isNotNull);
    });
  });
}

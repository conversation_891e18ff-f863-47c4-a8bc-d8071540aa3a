import 'package:flutter_test/flutter_test.dart';
import 'package:dasso_reader/utils/kokoro_model_validator.dart';

void main() {
  group('KOKORO Model Validation Tests', () {
    test('Check if KOKORO model files are present', () async {
      // Test model validation
      final bool isReady = await KokoroModelValidator.isModelReady();
      print('KOKORO Model Ready: $isReady');
      
      // Get detailed model information
      final Map<String, dynamic> modelInfo = await KokoroModelValidator.getModelInfo();
      print('Model Info: $modelInfo');
      
      // Get available Chinese voices
      final List<String> voices = await KokoroModelValidator.getAvailableChineseVoices();
      print('Available Chinese Voices: $voices');
      
      // This test will show us what files are present and missing
      expect(modelInfo, isNotNull);
    });
  });
}
